import React, { useEffect, useState } from 'react'
import { createContact, migrateContact, updateLead, updateLeadStage } from '../../../../../logic/apis/contact'
import { useParams } from 'react-router-dom'
import { getKeysFromObjects, isSuccess, notify, getValueByKeyAndMatch } from '../../../../../shared/helpers/util'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../../../../styles/styled'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import Button from '../../../../../shared/components/button/Button'
import { getStages } from '../../../../../logic/apis/sales'
import { StageGroupEnum } from '../../../../../shared/helpers/constants'
import AutoCompleteIndentation from '../../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import { AddNewContactModal, mergeSourceAndCampaignNames } from '../../addNewContactModal/AddNewContactModal'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../../leadSource/LeadSource'
import { fetchSearchReferer, getReferralNameFromID } from '../ContactProfile'
import SearchableDropdown from '../../../../../shared/searchableDropdown/SearchableDropdown'
import { hasValueChanged } from '../../../../opportunity/Opportunity'
import { CustomModal } from '../../../../../shared/customModal/CustomModal'
import ContactMergeModal from './ContactMergeModal'

const stageSchema = Yup.object().shape({
  stage: Yup.string().required('Stage is required'),
})

const LeadStage = ({
  initFetchContact,
  fetchActivity,
  stages,
  leads,
  initFetch,
  leadSrcData,
  refererres,
  projectTypesDrop,
  setShowCreateRuleDialog,
  setTrackingRuleId,
  setSelectedLeadId,
  officeDrop,
}: {
  initFetchContact: () => void
  stages: any[]
  fetchActivity: () => void
  leads: any[]
  initFetch: () => void
  leadSrcData: any[]
  refererres: any[]
  projectTypesDrop: any[]
  setShowCreateRuleDialog: (value: React.SetStateAction<boolean>) => void
  setTrackingRuleId: (value: React.SetStateAction<string>) => void
  setSelectedLeadId: (value: React.SetStateAction<string>) => void
  officeDrop?: any
}) => {
  const { contactId } = useParams()
  const [submitLoading, setSubmitLoading] = useState(false)
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState()
  const [referrerName, setReferrerName] = useState('')
  const [createContactDetails, setCreateContactDetails] = useState<any>({})
  const [matchingContacts, setMatchingContacts] = useState<any[]>([])
  const [mergeModal, setMergeModal] = useState(false)
  const [clientData, setClientData] = useState<any>({})
  const [createdClient, setCreatedClient] = useState({})

  const [referrerModal, setShowReferrerModal] = useState(false)
  const [localReferrerData, setLocalReferrerData] = useState<any>({
    leadSource: '',
    name: '',
    id: '',
  })

  // Fetch stages on component mount
  const activeLead = leads?.find((v) => v.status === 'active')

  useEffect(() => {
    if (activeLead?.referredBy) {
      getReferralNameFromID(activeLead?.referredBy).then((name) => {
        setReferrerName(name)
      })
    }
  }, [activeLead?.referredBy])

  const handleMergeCancel = async () => {
    try {
      if (createContactDetails._id) {
        return
      }
      let response = await createContact(createContactDetails)
      if (isSuccess(response)) {
        setClientData(createContactDetails)
        setCreatedClient(createContactDetails)
        notify('Contact Created Successfully', 'success')
      }
    } catch (error) {
      setClientData({})
      setCreatedClient({})
      console.log({ error })
    } finally {
      setMergeModal(false)
    }
  }

  const handleMergeConfirm = async ({ mergedFields, toContact }: any) => {
    try {
      const res = await migrateContact(toContact, mergedFields)
      if (isSuccess(res)) {
        notify('Contact Profile Merged Successfully', 'success')
        setClientData(res?.data?.data?.contact)
        setCreatedClient(res?.data?.data?.contact)

        setLocalReferrerData({
          leadSource: '',
          name: '',
          id: '',
        })
        setShowReferrerModal(false)
      }
    } catch (error) {
    } finally {
      setMergeModal(false)
    }
  }

  const handleStageChange = async (values: { stage: string }) => {
    try {
      setSubmitLoading(true)
      const stageId = getValueByKeyAndMatch('_id', values.stage, 'name', stages)
      const response = await updateLeadStage(stageId, activeLead?._id!)
      if (isSuccess(response)) {
        notify('Stage changed!', 'success')
        initFetchContact()
        fetchActivity()
      }
    } catch (err) {
      notify('Failed to change stage!', 'error')
      console.log('ERR', err)
    } finally {
      setSubmitLoading(false)
    }
  }

  // Get current stage name for initial value
  const getCurrentStageName = () => {
    if (leads?.length && stages.length > 0) {
      return getValueByKeyAndMatch('name', activeLead?.stageId, '_id', stages) || ''
    }
    return ''
  }

  const handleInputBlurValue = async (data: any) => {
    try {
      const hasValues = Object.values(data)?.filter(Boolean)
      if (hasValues.length) {
        const res = await updateLead(data, activeLead?._id)
        if (isSuccess(res)) {
          notify('Lead updated!', 'success')
          initFetch()
          if ('leadSourceId' in data || 'campaignId' in data || 'referredBy' in data) {
            initFetchContact()
          }
        }
      }
    } catch (error) {
      console.log({ error })
    }
  }

  return (
    <>
      <Formik
        initialValues={{
          stage: getCurrentStageName(),
          newLeadDate: activeLead?.newLeadDate || '',
          leadSourceName:
            activeLead?.campaignId || activeLead?.leadSourceId
              ? getLeadSrcDropdownName(activeLead?.campaignId || activeLead?.leadSourceId, leadSrcData)?.sourceName
              : '',
          referredBy: referrerName || '',
          workType: getValueByKeyAndMatch('name', activeLead?.workType, 'id', projectTypesDrop) || '',
        }}
        validationSchema={stageSchema}
        onSubmit={() => {}}
        enableReinitialize
      >
        {({ values, errors, touched, setFieldValue }) => {
          useEffect(() => {
            if (values.leadSourceName !== '') {
              const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
              setSelectedLeadSourceObject(result?.leadSourceObject)
            }
          }, [values.leadSourceName])
          return (
            <Form>
              <SharedStyled.ContentHeader textAlign="left" as="h3">
                Lead Info
              </SharedStyled.ContentHeader>
              <SharedStyled.FlexCol margin="0px 0 0 0" width="100%" alignItems="center">
                <CustomSelect
                  labelName="Stage"
                  stateName="stage"
                  error={touched.stage && errors.stage ? true : false}
                  setFieldValue={setFieldValue}
                  value={values.stage}
                  dropDownData={getKeysFromObjects(stages, 'name')}
                  setValue={() => {}}
                  innerHeight="52px"
                  margin="10px 0 0 0"
                  disabled={submitLoading}
                  onBlur={() => {
                    handleStageChange(values)
                  }}
                />

                <SharedDateAndTime
                  value={values?.newLeadDate || ''}
                  labelName="Lead Date"
                  stateName="newLeadDate"
                  error={!!(touched?.newLeadDate && errors?.newLeadDate)}
                  setFieldValue={setFieldValue}
                  onBlur={() => {
                    handleInputBlurValue({ newLeadDate: new Date(values?.newLeadDate) })
                  }}
                />

                {
                  <>
                    {!!!activeLead?.leadSourceId ? (
                      <div style={{ margin: '5px auto -8px 0' }}>
                        <SharedStyled.Text
                          color="red"
                          variant="link"
                          fontSize="12px"
                          textDecoration="underline"
                          onClick={() => {
                            setShowCreateRuleDialog(true)
                            setSelectedLeadId(activeLead?._id)
                          }}
                        >
                          No Lead Source! Click to Create
                        </SharedStyled.Text>
                      </div>
                    ) : activeLead?.trackingRuleId ? (
                      <div style={{ margin: '5px auto -8px 0' }}>
                        <SharedStyled.Text
                          color="green"
                          variant="link"
                          fontSize="12px"
                          textDecoration="underline"
                          onClick={() => {
                            setTrackingRuleId(activeLead?.trackingRuleId)
                            setSelectedLeadId(activeLead?._id)
                            setShowCreateRuleDialog(true)
                          }}
                        >
                          Lead Source Matched! Click to Edit
                        </SharedStyled.Text>
                      </div>
                    ) : (
                      <></>
                    )}
                  </>
                }

                {leadSrcData?.length ? (
                  <AutoCompleteIndentation
                    labelName="Lead Source"
                    stateName={`leadSourceName`}
                    isLeadSource
                    dropdownHeight="180px"
                    error={touched.leadSourceName && errors.leadSourceName ? true : false}
                    borderRadius="0px"
                    setFieldValue={setFieldValue}
                    options={mergeSourceAndCampaignNames(leadSrcData)}
                    formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                    value={values.leadSourceName!}
                    setValueOnClick={(val: string) => {
                      setFieldValue('leadSourceName', val)
                    }}
                    className="material-autocomplete"
                    isIndentation={true}
                    onBlur={(name: string) => {
                      const result = getLeadSrcDropdownId(name || '', leadSrcData)
                      const leadSourceId = result?.leadSourceId
                      const campaignId = result?.campaignId || null
                      console.log({ result })
                      const data = {
                        leadSourceId,
                        campaignId,
                      }
                      handleInputBlurValue(data)
                    }}
                  />
                ) : null}
                {selectedLeadSourceObject?.code === 'referral' && (
                  <SharedStyled.FlexBox width="95%" justifyContent="end" margin="0 0 0 auto">
                    <SearchableDropdown
                      label="Referrer"
                      placeholder="Type to search"
                      searchFunction={fetchSearchReferer}
                      displayKey={'name'}
                      refererOptions={refererres?.slice(0, 20)}
                      onSelect={(item: any) => {
                        setFieldValue('referredBy', item?.name)

                        const referredBy = item?._id
                        handleInputBlurValue({ referredBy })
                      }}
                      selectedValue={values.referredBy}
                      handleBlur={() => {}}
                      resultExtractor={(res) => res?.data?.data?.referrers || []}
                      showAddOption
                      onAddClick={() => {
                        setShowReferrerModal?.(true)
                      }}
                      onTextChange={(text) => {
                        setLocalReferrerData?.({
                          leadSource: values?.leadSourceName,
                          name: text,
                        })
                      }}
                      showUnKnownOption
                      onUnKnownClick={() => {
                        setFieldValue('referredBy', 'Unknown')

                        handleInputBlurValue({ referredBy: 'unknown' })
                      }}
                    />
                  </SharedStyled.FlexBox>
                )}
                <CustomSelect
                  labelName="Work Type"
                  stateName="workType"
                  value={values?.workType || ''}
                  error={!!(touched?.workType && errors?.workType)}
                  setFieldValue={setFieldValue}
                  setValue={() => {}}
                  dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                  innerHeight="52px"
                  margin="10px 0 0 0"
                  onBlur={() => {
                    // if (values?.workType === lead.workType) return
                    const workType = getValueByKeyAndMatch('id', values.workType, 'name', projectTypesDrop)
                    handleInputBlurValue({ workType })
                  }}
                />
              </SharedStyled.FlexCol>
            </Form>
          )
        }}
      </Formik>

      <CustomModal show={referrerModal}>
        {/* <ReferrerModal
                onClose={() => {
                  setShowReferrerModal(false)
                }}
                onComplete={() => {
                  initFetchReferrers()
                }}
              /> */}

        <AddNewContactModal
          setShowAddNewClientModal={setShowReferrerModal}
          // setDetailsUpdate={setDetailsUpdate}
          // detailsUpdate={detailsUpdate}
          isOpportunity
          isReferrer
          noLoadScript
          clientName={localReferrerData?.name}
          officeDrop={officeDrop}
          refererres={refererres}
          setShowReferrerModal={setShowReferrerModal}
          mergeContact={{ setCreateContactDetails, setMatchingContacts, setMergeModal }}
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={async ({ id, name }: any) => {
            if (hasValueChanged(referrerName, name)) {
              handleInputBlurValue({
                referredBy: id,
              })
            }
            setLocalReferrerData({
              leadSource: '',
              name: '',
              id: '',
            })
            setShowReferrerModal(false)
          }}
        />
      </CustomModal>

      <CustomModal show={mergeModal}>
        <ContactMergeModal
          existingContacts={[createContactDetails, ...matchingContacts.slice(1)].sort((a, b) => {
            // Sort by createdAt (older first)
            const dateA = new Date(a.createdAt).getTime()
            const dateB = new Date(b.createdAt).getTime()

            // Place items without _id at the end
            if (!a._id && b._id) return 1
            if (a._id && !b._id) return -1
            if (!a._id && !b._id) return 0

            return dateA - dateB
          })}
          isCancel={!!createContactDetails._id}
          newContactData={matchingContacts[0]}
          onClose={() => {
            handleMergeCancel()
          }}
          onMerge={handleMergeConfirm}
          isNewContact={true}
        />
      </CustomModal>
    </>
  )
}

export default LeadStage
