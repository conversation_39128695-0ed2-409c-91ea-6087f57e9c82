import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import {
  dayjsFormat,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'

import { IntendWidth } from '../../../style'
import { InputWithValidation } from '../../../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../../shared/date/SharedDate'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import { Form, Formik } from 'formik'
import AutoCompleteIndentation from '../../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { AddNewContactModal, mergeSourceAndCampaignNames } from '../../addNewContactModal/AddNewContactModal'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../../leadSource/LeadSource'
import { createContact, migrateContact, updateLead } from '../../../../../logic/apis/contact'
import SearchableDropdown from '../../../../../shared/searchableDropdown/SearchableDropdown'
import { fetchSearchReferer, getReferralNameFromID } from '../ContactProfile'
import styled from 'styled-components'
import { CustomModal } from '../../../../../shared/customModal/CustomModal'
import { hasValueChanged } from '../../../../opportunity/Opportunity'
import ContactMergeModal from './ContactMergeModal'

interface LeadDateSectionProps {
  lead: any
  leadIndex: number
  leadSrcData: any[]
  refererres: any[]
  projectTypesDrop: any[]
  toggleCount: (heading: string) => void
  toggleHeading: any
  initFetch: any
  initFetchContact: any
  renderValidButton: React.ReactNode
  renderLostButton: React.ReactNode
  setShowCreateRuleDialog: (value: React.SetStateAction<boolean>) => void
  setTrackingRuleId: (value: React.SetStateAction<string>) => void
  setSelectedLeadId: (value: React.SetStateAction<string>) => void
  officeDrop: any
}

interface TrackingEntry {
  [key: string]: any
  createdAt: string
}
const EntryWrapper = styled.div<{ indent?: boolean }>`
  display: flex;
  flex-direction: column;
  padding-left: ${(props) => (props.indent ? '20px' : '0')};
  gap: 4px;
`

const EntryItem = styled.span`
  font-size: 14px;
  color: #333;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;

  strong {
    margin-right: 6px;
    color: #111;
  }
`
const LeadInfo: React.FC<LeadDateSectionProps> = ({
  leadIndex,
  lead,
  leadSrcData,
  refererres,
  projectTypesDrop,
  toggleCount,
  toggleHeading,
  initFetch,
  initFetchContact,
  renderValidButton,
  renderLostButton,
  setTrackingRuleId,
  setShowCreateRuleDialog,
  setSelectedLeadId,
  officeDrop,
}) => {
  const [referrerName, setReferrerName] = useState('')
  const [createContactDetails, setCreateContactDetails] = useState<any>({})
  const [matchingContacts, setMatchingContacts] = useState<any[]>([])
  const [mergeModal, setMergeModal] = useState(false)
  const [clientData, setClientData] = useState<any>({})
  const [createdClient, setCreatedClient] = useState({})

  const [referrerModal, setShowReferrerModal] = useState(false)
  const [localReferrerData, setLocalReferrerData] = useState<any>({
    leadSource: '',
    name: '',
    id: '',
  })

  useEffect(() => {
    if (lead?.referredBy) {
      getReferralNameFromID(lead?.referredBy).then((name) => {
        setReferrerName(name)
      })
    }
  }, [lead?.referredBy])

  const initialValues = {
    newLeadDate: lead ? dayjsFormat(lead?.newLeadDate, 'YYYY-MM-DDTHH:mm') : '',
    leadSourceId: lead ? lead?.leadSourceId : '',
    trackingRuleId: lead ? lead?.trackingRuleId : '',
    leadSourceName:
      lead?.campaignId || lead?.leadSourceId
        ? getLeadSrcDropdownName(lead?.campaignId || lead?.leadSourceId, leadSrcData)?.sourceName ||
          lead?.campaignName ||
          lead?.leadSourceName
        : '',
    referredBy: lead ? referrerName : '',
    workType: lead ? getValueByKeyAndMatch('name', lead?.workType, 'id', projectTypesDrop) : '',
    invalidLeadReason: lead ? lead?.invalidLeadReason?.reason : '',
    invalidLeadNote: lead ? lead?.invalidLeadReason?.notes : '',
    lostDate: lead ? dayjsFormat(lead?.lostDate, 'YYYY-MM-DDTHH:mm') : '',
    lostReason: lead ? lead?.lostReason?.reason : '',
    lostNote: lead ? lead?.lostReason?.notes : '',
  }
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState()

  const handleInputBlurValue = async (data: any) => {
    try {
      const hasValues = Object.values(data)?.filter(Boolean)
      if (hasValues.length) {
        const res = await updateLead(data, lead?._id)
        if (isSuccess(res)) {
          notify('Lead updated!', 'success')
          initFetch()
          if ('leadSourceId' in data || 'campaignId' in data || 'referredBy' in data) {
            initFetchContact()
          }
        }
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const renderKeyValueList = (entry: TrackingEntry, indent: boolean = false) => {
    const filtered = Object.entries(entry || {})?.filter(([key, value]) => value !== null && value !== '')

    const sorted = [
      ...(entry?.createdAt ? [['createdAt', dayjsFormat(entry.createdAt, 'M/D/YY h:mm A')]] : []),
      ...filtered.filter(([key]) => key !== 'createdAt'),
    ]

    return (
      <EntryWrapper indent={indent}>
        {sorted.map(([key, value]) => (
          <EntryItem key={key}>
            <strong>{key}:</strong> {value}
          </EntryItem>
        ))}
      </EntryWrapper>
    )
  }

  const handleMergeCancel = async () => {
    try {
      if (createContactDetails._id) {
        return
      }
      let response = await createContact(createContactDetails)
      if (isSuccess(response)) {
        setClientData(createContactDetails)
        setCreatedClient(createContactDetails)
        notify('Contact Created Successfully', 'success')
      }
    } catch (error) {
      setClientData({})
      setCreatedClient({})
      console.log({ error })
    } finally {
      setMergeModal(false)
    }
  }

  const handleMergeConfirm = async ({ mergedFields, toContact }: any) => {
    try {
      const res = await migrateContact(toContact, mergedFields)
      if (isSuccess(res)) {
        notify('Contact Profile Merged Successfully', 'success')
        setClientData(res?.data?.data?.contact)
        setCreatedClient(res?.data?.data?.contact)

        setLocalReferrerData({
          leadSource: '',
          name: '',
          id: '',
        })
        setShowReferrerModal(false)
      }
    } catch (error) {
    } finally {
      setMergeModal(false)
    }
  }

  return (
    <SharedStyled.FlexCol padding="0 0 0 25px">
      <>
        <SharedStyled.Text
          color={lead?.status === 'active' ? 'green' : 'unset'}
          variant="link"
          fontWeight="700"
          fontSize="18px"
          margin="10px auto 0 0"
        >
          {' '}
          <span onClick={() => toggleCount(`${leadIndex}`)}>
            <span style={{ color: '#000', fontSize: '14px' }}>{toggleHeading[`${leadIndex}`] ? '▼' : '▶'}</span>
            &nbsp; {dayjsFormat(lead?.newLeadDate, 'M/D/YY')} {lead?.status === 'active' ? '(Active)' : ''}
          </span>
        </SharedStyled.Text>

        {toggleHeading[`${leadIndex}`] && (
          <>
            <Formik
              initialValues={initialValues}
              // validationSchema={validationSchema}
              onSubmit={() => {}}
              enableReinitialize={true}
            >
              {({ values, errors, touched, setFieldValue }) => {
                useEffect(() => {
                  if (values.leadSourceName !== '') {
                    const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                    setSelectedLeadSourceObject(result?.leadSourceObject)
                  }
                }, [values.leadSourceName])

                return (
                  <Form style={{ width: '100%' }}>
                    <SharedStyled.FlexCol>
                      {lead?.status === 'converted' && (
                        <SharedStyled.Text fontWeight="700" fontSize="16px" margin="10px auto 0 0">
                          Converted on {dayjsFormat(lead?.convertedDate, 'M/D/YY h:mm A')}
                        </SharedStyled.Text>
                      )}
                      <SharedDateAndTime
                        value={values?.newLeadDate || ''}
                        labelName="Lead Date"
                        stateName="newLeadDate"
                        error={!!(touched?.newLeadDate && errors?.newLeadDate)}
                        setFieldValue={setFieldValue}
                        onBlur={() => {
                          handleInputBlurValue({ newLeadDate: new Date(values?.newLeadDate) })
                        }}
                      />

                      <div
                        style={{
                          width: '100%',
                        }}
                      >
                        {
                          <div style={{ margin: '5px auto -8px 0' }}>
                            {!!!values?.leadSourceId ? (
                              <SharedStyled.Text
                                color="red"
                                fontSize="12px"
                                variant="link"
                                textDecoration="underline"
                                onClick={() => {
                                  setShowCreateRuleDialog(true)
                                  setSelectedLeadId(lead?._id)
                                }}
                              >
                                No Lead Source! Click to Create
                              </SharedStyled.Text>
                            ) : values?.trackingRuleId ? (
                              <SharedStyled.Text
                                color="green"
                                fontSize="12px"
                                variant="link"
                                textDecoration="underline"
                                onClick={() => {
                                  setTrackingRuleId(values?.trackingRuleId)
                                  setSelectedLeadId(lead?._id)
                                  setShowCreateRuleDialog(true)
                                }}
                              >
                                Lead Source Matched! Click to Edit
                              </SharedStyled.Text>
                            ) : (
                              <></>
                            )}
                          </div>
                        }
                        {leadSrcData?.length ? (
                          <AutoCompleteIndentation
                            labelName="Lead Source"
                            stateName={`leadSourceName`}
                            isLeadSource
                            dropdownHeight="180px"
                            error={touched.leadSourceName && errors.leadSourceName ? true : false}
                            borderRadius="0px"
                            setFieldValue={setFieldValue}
                            options={mergeSourceAndCampaignNames(leadSrcData)}
                            formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                            value={values.leadSourceName!}
                            setValueOnClick={(val: string) => {
                              setFieldValue('leadSourceName', val)
                            }}
                            className="material-autocomplete"
                            isIndentation={true}
                            onBlur={(name: string) => {
                              const result = getLeadSrcDropdownId(name || '', leadSrcData)
                              const leadSourceId = result?.leadSourceId
                              const campaignId = result?.campaignId || null
                              console.log({ result })
                              const data = {
                                leadSourceId,
                                campaignId,
                              }
                              handleInputBlurValue(data)
                            }}
                          />
                        ) : null}
                        {selectedLeadSourceObject?.code === 'referral' && (
                          <SharedStyled.FlexBox width="95%" justifyContent="end" margin="0 0 0 auto">
                            <SearchableDropdown
                              label="Referrer"
                              placeholder="Type to search"
                              searchFunction={fetchSearchReferer}
                              displayKey={'name'}
                              refererOptions={refererres?.slice(0, 20)}
                              onSelect={(item: any) => {
                                setFieldValue('referredBy', item?.name)

                                const referredBy = item?._id
                                handleInputBlurValue({ referredBy })
                              }}
                              selectedValue={values.referredBy}
                              handleBlur={() => {}}
                              resultExtractor={(res) => res?.data?.data?.referrers || []}
                              showAddOption
                              onTextChange={(text) => {
                                setLocalReferrerData?.({
                                  leadSource: values?.leadSourceName,
                                  name: text,
                                })
                              }}
                              onAddClick={() => {
                                setShowReferrerModal?.(true)
                              }}
                              showUnKnownOption
                              onUnKnownClick={() => {
                                setFieldValue('referredBy', 'Unknown')

                                handleInputBlurValue({ referredBy: 'unknown' })
                              }}
                            />
                          </SharedStyled.FlexBox>
                        )}
                      </div>

                      <CustomSelect
                        labelName="Work Type"
                        stateName="workType"
                        value={values?.workType || ''}
                        error={!!(touched?.workType && errors?.workType)}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                        innerHeight="52px"
                        margin="10px 0 0 0"
                        onBlur={() => {
                          // if (values?.workType === lead.workType) return
                          const workType = getValueByKeyAndMatch('id', values.workType, 'name', projectTypesDrop)
                          handleInputBlurValue({ workType })
                        }}
                      />

                      {lead?.status !== 'lost' && lead?.status === 'invalid' && (
                        <>
                          <SharedStyled.FlexRow gap="0px" alignItems="center">
                            <CustomSelect
                              labelName="Invalid Lead Reason"
                              stateName="invalidLeadReason"
                              value={values?.invalidLeadReason || ''}
                              error={!!(touched?.invalidLeadReason && errors?.invalidLeadReason)}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              disabled={true}
                              dropDownData={[]}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />
                            <div style={{ margin: '10px 0 0 5px' }}>{renderValidButton}</div>
                          </SharedStyled.FlexRow>

                          <IntendWidth>
                            <InputWithValidation
                              labelName="Invalid Lead Notes"
                              stateName="invalidLeadNote"
                              disabled={true}
                              error={touched.invalidLeadNote && errors.invalidLeadNote ? true : false}
                            />
                          </IntendWidth>
                        </>
                      )}

                      {lead?.status !== 'invalid' && lead?.status === 'lost' && (
                        <>
                          <SharedDate
                            value={values?.lostDate || ''}
                            labelName="Lost Date"
                            stateName="lostDate"
                            disabled={true}
                            error={!!(touched?.lostDate && errors?.lostDate)}
                            setFieldValue={setFieldValue}
                          />
                          <SharedStyled.FlexRow gap="0px" alignItems="center">
                            <CustomSelect
                              labelName="Lost Reason"
                              stateName="lostReason"
                              value={values?.lostReason || ''}
                              error={!!(touched?.lostReason && errors?.lostReason)}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              disabled={true}
                              dropDownData={[]}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />
                            <div style={{ margin: '10px 0 0 5px' }}>{renderLostButton}</div>
                          </SharedStyled.FlexRow>

                          <IntendWidth>
                            <InputWithValidation
                              labelName="Lost Reason Notes"
                              stateName="lostNote"
                              disabled={true}
                              error={touched.lostNote && errors.lostNote ? true : false}
                            />
                          </IntendWidth>
                        </>
                      )}
                    </SharedStyled.FlexCol>
                  </Form>
                )
              }}
            </Formik>

            {lead?.tracking && (
              <>
                <SharedStyled.Text fontWeight="700" fontSize="16px" margin="10px auto 0 0">
                  Tracking
                </SharedStyled.Text>
                {renderKeyValueList(lead?.tracking, true)}
              </>
            )}
          </>
        )}
      </>

      <CustomModal show={referrerModal}>
        {/* <ReferrerModal
                onClose={() => {
                  setShowReferrerModal(false)
                }}
                onComplete={() => {
                  initFetchReferrers()
                }}
              /> */}

        <AddNewContactModal
          setShowAddNewClientModal={setShowReferrerModal}
          // setDetailsUpdate={setDetailsUpdate}
          // detailsUpdate={detailsUpdate}
          isOpportunity
          isReferrer
          noLoadScript
          clientName={localReferrerData?.name}
          officeDrop={officeDrop}
          refererres={refererres}
          setShowReferrerModal={setShowReferrerModal}
          mergeContact={{ setCreateContactDetails, setMatchingContacts, setMergeModal }}
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={async ({ id, name }: any) => {
            if (hasValueChanged(referrerName, name)) {
              handleInputBlurValue({
                referredBy: id,
              })
            }
            setLocalReferrerData({
              leadSource: '',
              name: '',
              id: '',
            })
            setShowReferrerModal(false)
          }}
        />
      </CustomModal>

      <CustomModal show={mergeModal}>
        <ContactMergeModal
          existingContacts={[createContactDetails, ...matchingContacts.slice(1)].sort((a, b) => {
            // Sort by createdAt (older first)
            const dateA = new Date(a.createdAt).getTime()
            const dateB = new Date(b.createdAt).getTime()

            // Place items without _id at the end
            if (!a._id && b._id) return 1
            if (a._id && !b._id) return -1
            if (!a._id && !b._id) return 0

            return dateA - dateB
          })}
          isCancel={!!createContactDetails._id}
          newContactData={matchingContacts[0]}
          onClose={() => {
            handleMergeCancel()
          }}
          onMerge={handleMergeConfirm}
          isNewContact={true}
        />
      </CustomModal>
    </SharedStyled.FlexCol>
  )
}

export default LeadInfo
